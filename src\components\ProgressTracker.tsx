import { Card } from "@/components/ui/card";
import { Star, Award, Target } from "lucide-react";

interface ProgressTrackerProps {
  exploredLetters: Set<string>;
  gameScore: number;
}

export function ProgressTracker({ exploredLetters, gameScore }: ProgressTrackerProps) {
  const totalLetters = 26;
  const exploredCount = exploredLetters.size;
  const progressPercentage = (exploredCount / totalLetters) * 100;

  const getProgressColor = () => {
    if (progressPercentage >= 75) return 'bg-success';
    if (progressPercentage >= 50) return 'bg-rainbow-blue';
    if (progressPercentage >= 25) return 'bg-rainbow-orange';
    return 'bg-rainbow-red';
  };

  const getProgressMessage = () => {
    if (progressPercentage === 100) return "Amazing! You've explored all letters! 🎉";
    if (progressPercentage >= 75) return "Fantastic! You're almost done! 🌟";
    if (progressPercentage >= 50) return "Great job! Keep exploring! 🚀";
    if (progressPercentage >= 25) return "Good start! Keep going! 💪";
    return "Welcome! Start exploring letters! ✨";
  };

  return (
    <Card className="p-6 shadow-playful">
      <div className="flex items-center gap-3 mb-4">
        <Award className="w-6 h-6 text-primary" />
        <h3 className="text-xl font-bold text-foreground">Your Progress</h3>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-muted-foreground">
            Letters Explored
          </span>
          <span className="text-sm font-bold text-foreground">
            {exploredCount} / {totalLetters}
          </span>
        </div>
        <div className="w-full bg-muted rounded-full h-3 overflow-hidden">
          <div
            className={`h-full ${getProgressColor()} transition-all duration-500 rounded-full`}
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        <p className="text-sm text-muted-foreground mt-2 text-center">
          {getProgressMessage()}
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center p-4 bg-gradient-to-br from-primary/10 to-primary/5 rounded-xl">
          <Star className="w-8 h-8 text-secondary mx-auto mb-2 fill-current" />
          <div className="text-2xl font-bold text-foreground">{exploredCount}</div>
          <div className="text-sm text-muted-foreground">Letters</div>
        </div>
        
        <div className="text-center p-4 bg-gradient-to-br from-success/10 to-success/5 rounded-xl">
          <Target className="w-8 h-8 text-success mx-auto mb-2" />
          <div className="text-2xl font-bold text-foreground">{gameScore}</div>
          <div className="text-sm text-muted-foreground">Game Score</div>
        </div>
      </div>

      {/* Achievement Badges */}
      {exploredCount >= 5 && (
        <div className="mt-4 p-3 bg-gradient-success rounded-xl text-center">
          <div className="text-2xl mb-1">🏆</div>
          <div className="text-sm font-medium text-success-foreground">
            Explorer Badge Earned!
          </div>
        </div>
      )}
    </Card>
  );
}