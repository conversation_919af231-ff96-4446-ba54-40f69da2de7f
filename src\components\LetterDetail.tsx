import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowLeft, Volume2 } from "lucide-react";

interface LetterDetailProps {
  letter: string;
  onBack: () => void;
  onExplored: (letter: string) => void;
}

const letterWords: Record<string, { word: string; emoji: string }> = {
  A: { word: "Apple", emoji: "🍎" },
  B: { word: "Ball", emoji: "⚽" },
  C: { word: "Cat", emoji: "🐱" },
  D: { word: "Dog", emoji: "🐕" },
  E: { word: "Elephant", emoji: "🐘" },
  F: { word: "Fish", emoji: "🐠" },
  G: { word: "Giraffe", emoji: "🦒" },
  H: { word: "House", emoji: "🏠" },
  I: { word: "Ice cream", emoji: "🍦" },
  J: { word: "Juice", emoji: "🧃" },
  K: { word: "Kite", emoji: "🪁" },
  L: { word: "Lion", emoji: "🦁" },
  M: { word: "Mouse", emoji: "🐭" },
  N: { word: "Nest", emoji: "🪺" },
  O: { word: "Orange", emoji: "🍊" },
  P: { word: "Pig", emoji: "🐷" },
  Q: { word: "Queen", emoji: "👸" },
  R: { word: "Robot", emoji: "🤖" },
  S: { word: "Sun", emoji: "☀️" },
  T: { word: "Tree", emoji: "🌳" },
  U: { word: "Umbrella", emoji: "☂️" },
  V: { word: "Van", emoji: "🚐" },
  W: { word: "Whale", emoji: "🐋" },
  X: { word: "Xylophone", emoji: "🎹" },
  Y: { word: "Yo-yo", emoji: "🪀" },
  Z: { word: "Zebra", emoji: "🦓" },
};

const getLetterColor = (letter: string) => {
  const index = letter.charCodeAt(0) - 65; // A=0, B=1, etc.
  const colors = [
    'from-rainbow-red to-rainbow-orange',
    'from-rainbow-orange to-rainbow-yellow', 
    'from-rainbow-yellow to-rainbow-green',
    'from-rainbow-green to-rainbow-blue', 
    'from-rainbow-blue to-rainbow-purple',
    'from-rainbow-purple to-rainbow-pink', 
    'from-rainbow-pink to-rainbow-red'
  ];
  return colors[index % colors.length];
};

export function LetterDetail({ letter, onBack, onExplored }: LetterDetailProps) {
  const letterData = letterWords[letter];
  
  const handleSoundPlay = () => {
    onExplored(letter);
    // In a real app, this would play the letter and word pronunciation
    console.log(`Playing sound for ${letter} and ${letterData.word}`);
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-background to-muted">
      <Button
        onClick={onBack}
        variant="outline"
        className="absolute top-4 left-4 rounded-xl border-2"
      >
        <ArrowLeft className="w-4 h-4 mr-2" />
        Back
      </Button>

      <Card className="w-full max-w-md p-8 text-center shadow-playful bounce-in">
        <div className={`bg-gradient-to-br ${getLetterColor(letter)} rounded-3xl p-8 mb-6 shadow-glow`}>
          <div className="text-white font-bold text-8xl mb-2">{letter}</div>
          <div className="text-white/90 font-medium text-4xl">{letter.toLowerCase()}</div>
        </div>

        <div className="mb-6">
          <div className="text-6xl mb-4">{letterData.emoji}</div>
          <h2 className="text-3xl font-bold text-foreground mb-2">
            {letterData.word}
          </h2>
          <p className="text-muted-foreground text-lg">
            {letter} is for {letterData.word}
          </p>
        </div>

        <Button
          onClick={handleSoundPlay}
          size="lg"
          className="bg-gradient-primary text-white font-bold text-xl px-8 py-4 rounded-xl shadow-button letter-bounce"
        >
          <Volume2 className="w-6 h-6 mr-3" />
          Play Sound
        </Button>
      </Card>
    </div>
  );
}