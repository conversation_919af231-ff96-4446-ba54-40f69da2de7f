import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Volume2, Star, ArrowLeft } from "lucide-react";

interface GameModeProps {
  onBack: () => void;
  onLetterFound: (letter: string) => void;
}

const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');

const getLetterColor = (index: number) => {
  const colors = [
    'bg-rainbow-red', 'bg-rainbow-orange', 'bg-rainbow-yellow', 
    'bg-rainbow-green', 'bg-rainbow-blue', 'bg-rainbow-purple', 'bg-rainbow-pink'
  ];
  return colors[index % colors.length];
};

export function GameMode({ onBack, onLetterFound }: GameModeProps) {
  const [targetLetter, setTargetLetter] = useState('');
  const [score, setScore] = useState(0);
  const [showSuccess, setShowSuccess] = useState(false);
  const [gameLetters, setGameLetters] = useState<string[]>([]);

  useEffect(() => {
    startNewRound();
  }, []);

  const startNewRound = () => {
    const newTarget = letters[Math.floor(Math.random() * letters.length)];
    setTargetLetter(newTarget);
    
    // Create a set of 12 random letters including the target
    const shuffledLetters = [...letters].sort(() => Math.random() - 0.5);
    const roundLetters = shuffledLetters.slice(0, 11);
    if (!roundLetters.includes(newTarget)) {
      roundLetters[Math.floor(Math.random() * roundLetters.length)] = newTarget;
    }
    roundLetters.sort(() => Math.random() - 0.5);
    setGameLetters(roundLetters);
    setShowSuccess(false);
  };

  const handleLetterClick = (letter: string) => {
    if (letter === targetLetter) {
      setScore(score + 1);
      setShowSuccess(true);
      onLetterFound(letter);
      
      // In a real app, this would play success sound
      console.log(`Correct! Found letter: ${letter}`);
      
      setTimeout(() => {
        startNewRound();
      }, 2000);
    } else {
      // In a real app, this would play "try again" sound
      console.log(`Try again! Looking for: ${targetLetter}`);
    }
  };

  const playTargetSound = () => {
    // In a real app, this would play the target letter sound
    console.log(`Playing sound for target letter: ${targetLetter}`);
  };

  if (showSuccess) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gradient-to-br from-success/10 to-success/5">
        <Card className="w-full max-w-md p-8 text-center shadow-playful bounce-in">
          <div className="text-6xl mb-4">🎉</div>
          <h2 className="text-3xl font-bold text-success mb-4">Great Job!</h2>
          <p className="text-xl text-foreground mb-4">
            You found the letter <span className="font-bold text-success">{targetLetter}</span>!
          </p>
          <div className="flex justify-center mb-4">
            {[...Array(3)].map((_, i) => (
              <Star key={i} className="w-8 h-8 text-secondary fill-current star-sparkle mx-1" />
            ))}
          </div>
          <p className="text-muted-foreground">Getting ready for the next letter...</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 bg-gradient-to-br from-background to-muted">
      <div className="flex justify-between items-center mb-6">
        <Button
          onClick={onBack}
          variant="outline"
          className="rounded-xl border-2"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button>
        
        <div className="flex items-center gap-2 bg-card px-4 py-2 rounded-xl shadow-button">
          <Star className="w-5 h-5 text-secondary fill-current" />
          <span className="font-bold text-lg">{score}</span>
        </div>
      </div>

      <Card className="w-full max-w-2xl mx-auto p-6 shadow-playful">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-foreground mb-4">
            Find the Letter Challenge!
          </h2>
          <div className="bg-gradient-primary text-white p-6 rounded-2xl mb-4 shadow-glow">
            <p className="text-lg mb-3">Can you find the letter</p>
            <div className="text-6xl font-bold mb-3">{targetLetter}</div>
            <Button
              onClick={playTargetSound}
              variant="secondary"
              size="sm"
              className="rounded-xl"
            >
              <Volume2 className="w-4 h-4 mr-2" />
              Hear it again
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-4 gap-3 max-w-lg mx-auto">
          {gameLetters.map((letter, index) => (
            <Button
              key={`${letter}-${index}`}
              onClick={() => handleLetterClick(letter)}
              className={`
                ${getLetterColor(letters.indexOf(letter))} 
                text-white font-bold text-2xl 
                h-16 w-16 
                rounded-2xl shadow-button border-none
                letter-bounce hover:shadow-glow
                transition-all duration-200
              `}
              variant="default"
            >
              {letter}
            </Button>
          ))}
        </div>
      </Card>
    </div>
  );
}