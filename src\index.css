@tailwind base;
@tailwind components;
@tailwind utilities;

/* Alphabet Learning App Design System - Bright, playful colors perfect for children */

@layer base {
  :root {
    /* Base colors */
    --background: 245 100% 97%;
    --foreground: 220 15% 25%;
    
    /* Rainbow theme colors */
    --rainbow-red: 0 85% 60%;
    --rainbow-orange: 30 90% 55%;
    --rainbow-yellow: 50 95% 60%;
    --rainbow-green: 120 70% 50%;
    --rainbow-blue: 210 85% 60%;
    --rainbow-purple: 270 80% 60%;
    --rainbow-pink: 320 80% 65%;
    
    /* App specific colors */
    --primary: 210 85% 60%;
    --primary-glow: 210 90% 70%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 50 95% 60%;
    --secondary-foreground: 220 15% 25%;
    
    --success: 120 70% 50%;
    --success-foreground: 0 0% 100%;
    
    --accent: 320 80% 65%;
    --accent-foreground: 0 0% 100%;
    
    /* Gradients */
    --gradient-rainbow: linear-gradient(45deg, 
      hsl(var(--rainbow-red)), 
      hsl(var(--rainbow-orange)), 
      hsl(var(--rainbow-yellow)), 
      hsl(var(--rainbow-green)), 
      hsl(var(--rainbow-blue)), 
      hsl(var(--rainbow-purple))
    );
    
    --gradient-primary: linear-gradient(135deg, 
      hsl(var(--primary)), 
      hsl(var(--primary-glow))
    );
    
    --gradient-success: linear-gradient(135deg, 
      hsl(var(--success)), 
      hsl(120 80% 60%)
    );
    
    /* Shadows and effects */
    --shadow-playful: 0 8px 32px hsl(var(--primary) / 0.2);
    --shadow-button: 0 4px 16px hsl(var(--primary) / 0.3);
    --shadow-glow: 0 0 20px hsl(var(--primary-glow) / 0.5);
    
    /* Animation timings */
    --bounce-timing: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --smooth-timing: cubic-bezier(0.4, 0, 0.2, 1);

    /* UI elements */
    --card: 0 0% 100%;
    --card-foreground: 220 15% 25%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 220 15% 25%;
    
    --muted: 210 25% 95%;
    --muted-foreground: 220 15% 45%;
    
    --border: 210 25% 90%;
    --input: 0 0% 100%;
    --ring: 210 85% 60%;
    
    /* Border radius - more playful for children */
    --radius: 1rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

}


@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    background: linear-gradient(135deg, 
      hsl(var(--background)), 
      hsl(210 25% 95%)
    );
  }
}

/* Playful animations for children */
@layer utilities {
  .bounce-in {
    animation: bounceIn 0.6s var(--bounce-timing);
  }
  
  .wiggle {
    animation: wiggle 0.5s ease-in-out;
  }
  
  .star-sparkle {
    animation: sparkle 1s ease-in-out infinite;
  }
  
  .letter-bounce {
    transition: transform 0.2s var(--bounce-timing);
  }
  
  .letter-bounce:hover {
    transform: scale(1.1) rotate(-2deg);
  }
  
  .letter-bounce:active {
    transform: scale(0.95);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(-5deg); }
  75% { transform: rotate(5deg); }
}

@keyframes sparkle {
  0%, 100% { 
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
  50% { 
    transform: scale(1.2) rotate(180deg);
    opacity: 0.8;
  }
}