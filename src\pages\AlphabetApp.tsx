import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { AlphabetGrid } from "@/components/AlphabetGrid";
import { LetterDetail } from "@/components/LetterDetail";
import { GameMode } from "@/components/GameMode";
import { ProgressTracker } from "@/components/ProgressTracker";
import { BookOpen, Gamepad2, BarChart3 } from "lucide-react";

type View = 'home' | 'letter' | 'game' | 'progress';

export default function AlphabetApp() {
  const [currentView, setCurrentView] = useState<View>('home');
  const [selectedLetter, setSelectedLetter] = useState<string>('');
  const [exploredLetters, setExploredLetters] = useState<Set<string>>(new Set());
  const [gameScore, setGameScore] = useState(0);

  const handleLetterClick = (letter: string) => {
    setSelectedLetter(letter);
    setCurrentView('letter');
  };

  const handleLetterExplored = (letter: string) => {
    setExploredLetters(prev => new Set([...prev, letter]));
  };

  const handleGameLetterFound = (letter: string) => {
    setGameScore(prev => prev + 1);
    handleLetterExplored(letter);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'letter':
        return (
          <LetterDetail
            letter={selectedLetter}
            onBack={() => setCurrentView('home')}
            onExplored={handleLetterExplored}
          />
        );
      case 'game':
        return (
          <GameMode
            onBack={() => setCurrentView('home')}
            onLetterFound={handleGameLetterFound}
          />
        );
      case 'progress':
        return (
          <div className="min-h-screen p-4 bg-gradient-to-br from-background to-muted">
            <Button
              onClick={() => setCurrentView('home')}
              variant="outline"
              className="mb-6 rounded-xl border-2"
            >
              ← Back to Home
            </Button>
            <div className="max-w-md mx-auto">
              <ProgressTracker 
                exploredLetters={exploredLetters} 
                gameScore={gameScore} 
              />
            </div>
          </div>
        );
      default:
        return (
          <div className="min-h-screen p-4">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl sm:text-5xl font-bold bg-gradient-rainbow bg-clip-text text-transparent mb-4 bounce-in">
                ABC Learning
              </h1>
              <p className="text-lg text-muted-foreground">
                Tap letters to explore and learn! 🌟
              </p>
            </div>

            {/* Navigation Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 max-w-4xl mx-auto mb-8">
              <Card 
                className="p-6 cursor-pointer letter-bounce shadow-button hover:shadow-glow transition-all duration-200"
                onClick={() => setCurrentView('game')}
              >
                <div className="text-center">
                  <Gamepad2 className="w-12 h-12 text-primary mx-auto mb-3" />
                  <h3 className="font-bold text-lg mb-2">Play Game</h3>
                  <p className="text-sm text-muted-foreground">Find letters and earn stars!</p>
                </div>
              </Card>

              <Card 
                className="p-6 cursor-pointer letter-bounce shadow-button hover:shadow-glow transition-all duration-200"
                onClick={() => setCurrentView('progress')}
              >
                <div className="text-center">
                  <BarChart3 className="w-12 h-12 text-success mx-auto mb-3" />
                  <h3 className="font-bold text-lg mb-2">Progress</h3>
                  <p className="text-sm text-muted-foreground">See how you're doing!</p>
                </div>
              </Card>

              <Card className="p-6">
                <div className="text-center">
                  <BookOpen className="w-12 h-12 text-accent mx-auto mb-3" />
                  <h3 className="font-bold text-lg mb-2">Letters</h3>
                  <p className="text-sm text-muted-foreground">Explore {exploredLetters.size}/26 letters</p>
                </div>
              </Card>
            </div>

            {/* Alphabet Grid */}
            <AlphabetGrid 
              onLetterClick={handleLetterClick}
              exploredLetters={exploredLetters}
            />

            {/* Quick Stats */}
            <div className="text-center mt-8">
              <div className="inline-flex items-center gap-4 bg-card px-6 py-3 rounded-2xl shadow-button">
                <div className="flex items-center gap-2">
                  <span className="text-2xl">⭐</span>
                  <span className="font-bold">{exploredLetters.size}</span>
                  <span className="text-sm text-muted-foreground">explored</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-2xl">🎯</span>
                  <span className="font-bold">{gameScore}</span>
                  <span className="text-sm text-muted-foreground">found</span>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted">
      {renderCurrentView()}
    </div>
  );
}